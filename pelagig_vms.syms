00000000 N SVC_Count
00000000 n SVC_End
00000000 N SVC_Table
00000025 a TCB_STACKF
00000028 a TCB_TSTACK
08000000 T g_pfnVectors
0800018c W Reset_Handler
0800019e t CopyDataInit
080001a4 t LoopCopyDataInit
080001b2 t FillZ<PERSON><PERSON>s
080001b6 t <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s
080001be t <PERSON>Forever
080001d8 W ADC1_IRQHandler
080001d8 W BusFault_Handler
080001d8 W CAN1_RX0_IRQHandler
080001d8 W CAN1_RX1_IRQHandler
080001d8 W CAN1_SCE_IRQHandler
080001d8 W CAN1_TX_IRQHandler
080001d8 W COMP_IRQHandler
080001d8 W CRS_IRQHandler
080001d8 W DebugMon_Handler
080001d8 T Default_Handler
080001d8 W DMA1_Channel1_IRQHandler
080001d8 W DMA1_Channel2_IRQHandler
080001d8 W DMA1_Channel3_IRQHandler
080001d8 W DMA1_Channel4_IRQHandler
080001d8 W DMA1_Channel5_IRQHandler
080001d8 W DMA1_Channel6_IRQHandler
080001d8 W DMA1_Channel7_IRQHandler
080001d8 W DMA2_Channel1_IRQHandler
080001d8 W DMA2_Channel2_IRQHandler
080001d8 W DMA2_Channel3_IRQHandler
080001d8 W DMA2_Channel4_IRQHandler
080001d8 W DMA2_Channel5_IRQHandler
080001d8 W DMA2_Channel6_IRQHandler
080001d8 W DMA2_Channel7_IRQHandler
080001d8 W EXTI0_IRQHandler
080001d8 W EXTI15_10_IRQHandler
080001d8 W EXTI1_IRQHandler
080001d8 W EXTI2_IRQHandler
080001d8 W EXTI3_IRQHandler
080001d8 W EXTI4_IRQHandler
080001d8 W EXTI9_5_IRQHandler
080001d8 W FLASH_IRQHandler
080001d8 W FPU_IRQHandler
080001d8 W I2C1_ER_IRQHandler
080001d8 W I2C1_EV_IRQHandler
080001d8 W I2C2_ER_IRQHandler
080001d8 W I2C2_EV_IRQHandler
080001d8 W I2C3_ER_IRQHandler
080001d8 W I2C3_EV_IRQHandler
080001d8 t Infinite_Loop
080001d8 W LPTIM1_IRQHandler
080001d8 W LPTIM2_IRQHandler
080001d8 W LPUART1_IRQHandler
080001d8 W MemManage_Handler
080001d8 W NMI_Handler
080001d8 W PVD_PVM_IRQHandler
080001d8 W QUADSPI_IRQHandler
080001d8 W RCC_IRQHandler
080001d8 W RNG_IRQHandler
080001d8 W RTC_Alarm_IRQHandler
080001d8 W RTC_WKUP_IRQHandler
080001d8 W SAI1_IRQHandler
080001d8 W SDMMC1_IRQHandler
080001d8 W SPI1_IRQHandler
080001d8 W SPI2_IRQHandler
080001d8 W SPI3_IRQHandler
080001d8 W SWPMI1_IRQHandler
080001d8 W TAMP_STAMP_IRQHandler
080001d8 W TIM1_BRK_TIM15_IRQHandler
080001d8 W TIM1_CC_IRQHandler
080001d8 W TIM1_TRG_COM_IRQHandler
080001d8 W TIM1_UP_TIM16_IRQHandler
080001d8 W TIM2_IRQHandler
080001d8 W TIM6_DAC_IRQHandler
080001d8 W TIM7_IRQHandler
080001d8 W TSC_IRQHandler
080001d8 W UsageFault_Handler
080001d8 W USART1_IRQHandler
080001d8 W USART2_IRQHandler
080001d8 W USART3_IRQHandler
080001d8 W WWDG_IRQHandler
080001dc T rt_set_PSP
080001e2 T rt_get_PSP
080001e8 T os_set_env
08000200 T _alloc_box
0800021c T _free_box
08000238 T SVC_Handler
08000278 t SVC_ContextSave
0800029a t SVC_ContextRestore
080002bc t SVC_Exit
080002be t SVC_User
080002dc t SVC_Done
080002de T PendSV_Handler
080002e4 T Sys_Switch
0800033c t Sys_Exit
0800033e T SysTick_Handler
08000348 T OS_Tick_Handler
08000374 T rt_init_stack
08000478 t rt_ret_regs
080004a4 T rt_ret_val
080004c4 T rt_ret_val2
080004f0 t rt_ms2tick
08000558 t rt_tid2ptcb
08000596 t rt_id2obj
080005bc T svcKernelInitialize
08000654 T svcKernelStart
080006c4 T osKernelInitialize
08000708 T osKernelStart
080007f0 t sysThreadError
08000804 T svcThreadCreate
080008d4 T svcThreadGetId
08000900 T svcThreadTerminate
08000960 T osThreadCreate
080009c4 T osThreadGetId
080009f0 T osThreadExit
08000a14 T svcDelay
08000a40 T osDelay
08000a74 t rt_timer_insert
08000af8 t rt_timer_remove
08000b78 T svcTimerCreate
08000c1c T svcTimerStart
08000d38 T svcTimerStop
08000d7e T svcTimerCall
08000de0 T sysTimerTick
08000e68 T osTimerCreate
08000edc T osTimerStart
08000f18 T osTimerStop
08000f4c T osTimerCall
08000f80 T osTimerThread
08000fc4 T svcSignalSet
08001010 T svcSignalWait
080010dc T isrSignalSet
08001128 T osSignalSet
0800116c T osSignalWait
080011d0 T svcMutexCreate
08001220 T svcMutexWait
0800127a T svcMutexRelease
080012bc T osMutexCreate
08001318 T osMutexWait
08001354 T osMutexRelease
08001388 T svcMessageCreate
080013ea T svcMessageGet
080014b4 T isrMessagePut
08001500 T isrMessageGet
08001594 T osMessageGet
080015f0 T rt_evt_wait
080016a8 T rt_evt_set
08001764 T isr_evt_set
080017a0 T rt_evt_psh
08001848 t rt_inc_qi
080018a8 T rt_put_prio
08001934 T rt_get_first
08001994 T rt_put_rdy_first
080019c0 T rt_resort_prio
08001a10 T rt_put_dly
08001adc T rt_dec_dly
08001ba8 T rt_rmv_list
08001c10 T rt_rmv_dly
08001c6c T rt_psq_enq
08001cc4 T rt_mbx_init
08001d18 T rt_mbx_wait
08001e34 T rt_mbx_check
08001e58 T isr_mbx_send
08001e7c T isr_mbx_receive
08001efc T rt_mbx_psh
08002060 T _init_box
0800210c T rt_alloc_box
08002150 T rt_free_box
080021a6 T rt_init_mem
080021ee T rt_alloc_mem
08002296 T rt_free_mem
08002300 T rt_mut_init
08002338 T rt_mut_release
080024ac T rt_mut_wait
08002574 W rt_init_robin
0800259c W rt_chk_robin
08002604 T rt_sem_psh
08002654 t rt_systick_init
08002698 T rt_psh_req
080026cc T rt_pop_req
0800279c W os_tick_init
080027ac W os_tick_irqack
080027bc T rt_systick
0800280c W rt_stk_check
08002840 t rt_svc_init
080028c0 t rt_get_TID
08002908 t rt_init_context
08002998 T rt_switch_req
080029bc T rt_dispatch
08002a20 T rt_block
08002a74 T rt_tsk_self
08002a98 T rt_tsk_prio
08002b64 T rt_tsk_create
08002be8 T rt_tsk_delete
08002dd4 T rt_sys_init
08002edc T rt_sys_start
08002f2c T rt_dly_wait
08002f48 W SystemInit
08002fb8 T main
08003410 T device_pin_setup
08003424 T firmware_was_updated
08003510 t maybe_send_heartbeat
080035e0 t still_parked
08003620 t gps_one_shot
080036c8 T gps_fixed_integer
0800372c T gps_acquired_time
08003748 T gps_get_time
08003898 T gps_process_signals
080039c8 T gps_add_accel_info
08003a54 T gps_thread
08003fac T gps_set_high_res_tracking
08003fe0 T gps_set_periodic_tracking
08004058 T gps_set_normal_tracking
080040ac T gps_track_appropriately
08004128 T gps_set_state
080041c4 T gps_display_location
080042e4 T gps_display_satellites
08004394 T gps_settings_init
080043f0 T gps_settings_verify
080044b0 T gps_signal_setting_change
080044e8 T gps_settings_run_mode
0800455c T gps_settings_periodic
080045ec T gps_settings_event_set_idle
08004670 T gps_settings_idle_meters
080046ac T gps_settings_idle_time
080046f8 T gps_settings_idle
080047e4 T gps_settings_send
080048b4 T gps_settings_freq
080048fc T gps_setting_send_dump
080049b0 T gps_setting
08004a20 T gps_settings_dump
08004b64 T helm_thread
08004d04 T helm_calculate_sun
08004ddc T helm_power_manage
08004df8 T helm_board_log
08004e18 T helm_nap
08004e64 t helm_gps_watch
08004f04 t report_gps
08004f44 T modem_thread_retrieve_info
08004ff4 t modem_defaults_init
08005108 T modem_thread
08005310 T modem_should_run
080055b4 T modem_shedule_next_run
080055f8 T modem_check_for_network
08005668 T modem_attempt_upload
08005848 T modem_send_simple_message
08005898 T modem_settings_init
08005918 T modem_setting_dump
0800597c T modem_setting
08005ac8 t __NVIC_SystemReset
08005af4 T reboot_settings_init
08005b10 T reboot_tick
08005b50 T reboot_thread
08005de8 T reboot_check_pending
08005e10 T reboot_setting
08005ea8 T reboot_setting_dump
08005ed0 t gpio_write
08005f02 t gpio_read
08005f2c T battery_init
08005f90 T battery_read
08006018 T battery_charge_read
08006038 T battery_fault_read
08006058 T board_info_init
0800608c T board_info_read
080060c4 T board_info_write
080060f8 T board_info_set_imei
0800614c T board_info_set_build_crc
08006176 T board_info_read_build_crc
08006190 T board_info_ensure_nfc_imei
08006244 T board_log_record
080062e4 T boat_log_reset
0800631c T store_delta
08006378 T boat_log_get_delta
080063dc T boat_log_record
080067d4 T boat_log_gps
0800684c T cell_signal_record
080068c0 T compress_flatten
08006964 T console_write
0800697e T console_flush
0800698c t __NVIC_SystemReset
080069b8 T firmware_update
08006ab0 T firmware_is_valid
08006b3c T firmware_sensor_init
08006bec T flash_read_fill
08006ca0 T flash_read_init
08006cec T flash_read_seek
08006e30 T flash_read_block_raw
08006f6c T flash_read_block
08007102 T flash_read_page
0800720c T flash_store_init
08007250 T flash_store_reset
08007288 T flash_store_data_erased
080072c4 T flash_store_page_valid
0800731c T flash_store_erase
0800747c T flash_store_setup
080076c4 T flash_store_partial_okay
08007710 T flash_store_write
0800781a T flash_store_lock
08007844 T flash_store_unlock
0800785e T flash_store_set_busy
08007888 T flash_store_flush
08007c30 T flash_store_page_mark_free
08007d30 T distance_in_meters
08007f0c T heatshrink_encoder_reset
08007f66 T heatshrink_encoder_sink
08008020 T heatshrink_encoder_poll
08008168 T heatshrink_encoder_finish
080081b4 t st_step_search
080082de t st_yield_tag_bit
08008334 t st_yield_literal
0800838a t st_yield_br_index
080083d2 t st_yield_br_length
0800841e t st_save_backlog
08008464 t st_flush_bit_buffer
080084aa t add_tag_bit
080084cc t get_input_offset
080084e4 t get_input_buffer_size
080084fc t get_lookahead_size
08008512 t do_indexing
08008526 t is_finishing
08008542 t backlog_is_partial
0800855e t backlog_is_filled
0800857a t on_final_literal
08008596 t has_literal
080085b2 t can_take_byte
080085da t find_longest_match
080086d0 t push_outgoing_bits
08008738 t push_bits
080087f2 t push_literal_byte
08008832 t save_backlog
080088c8 T imei_copy
080088e4 t gpio_write
08008918 T modem_init
08008968 T modem_pin_setup
080089a0 T modem_send_packet
08008a18 T modem_state_label
08008cc4 T modem_power_on
08008d40 T modem_power_off
08008da0 T modem_report_crc_err
08008dfc T modem_crc_okay
08008e60 T modem_set_uint32
08008ea2 T modem_get_uint16
08008ece T modem_get_uint32
08008f04 T modem_get_64
08009078 T modem_read
080090e0 T modem_receive_ack_response
08009194 T modem_xmit_wait
080091b4 T modem_xmit_wait_test
08009310 T modem_send_data
08009340 T modem_send_data_vec
080094b4 T modem_get_status
080095d0 t modem_luhn
08009680 T modem_get_info
08009778 T modem_read_chunk
08009974 T modem_get_message_count
080099f8 T modem_delete_message
08009a24 T modem_log_new_entry
08009ab0 T is_nap_time
08009bd8 T nap_duration
08009c9c T parse_hour_minute
08009db0 T nap_setting_dump
08009e74 T nap_settings_init
08009ee4 T nap_setting
0800a0a0 T nmea_init
0800a0ba T nmea_fusedata
0800a298 T nmea_parsedata
0800a9dc T nmea_digit2dec
0800aa04 T pds_send_chunk
0800aad4 T pds_transmit_log_pages
0800ab94 T pds_send_log
0800adf8 T pds_firmware_acknowledge
0800ae68 T pds_firmware_updated
0800af44 T pds_retrieve_message
0800b238 T pds_check_messages
0800b374 T pds_settings_process
0800b400 T power_management
0800b538 T board_check_power
0800b56c T power_compute_average
0800b584 T provision_update
0800b5d8 T provision_factory
0800b5f4 T provision_shipping
0800b618 T provision_deployed
0800b630 T provision_board
0800b6e8 T shutdown_components_and_sleep
0800b78c T provision_setting
0800b830 T provision_setting_dump
0800b89c T settings_store
0800b8e4 T settings_init_all
0800b908 T settings_init
0800b958 T settings_dump
0800ba1c T setting_get_char
0800ba58 T setting_parse_line
0800bb00 T settings_execute
0800bbb8 T settings_parse
0800bcb4 T settings_find_key
0800bd04 T reset_setting
0800bd1c T solar_init
0800bd30 T solar_present
0800bd6c T solar_read
0800bdb8 T sun_position
0800be84 T is_nighttime
0800bee0 T sunrise_sunset_time
0800c550 T device_power_init
0800c560 T device_power_off
0800c590 T device_power_on
0800c5c4 t gpio_write
0800c5f8 T gps_power_on
0800c620 T gps_power_off
0800c640 T gps_init
0800c6bc T gps_factory_provision
0800c6c8 T gps_pin_setup
0800c720 T gps_send_command
0800c908 T gps_reset
0800c978 T gps_low_res_mode
0800c9cc T gps_highres_mode
0800ca20 T gps_periodic_mode
0800ca98 T gps_standby_mode
0800caa6 T lsm303d_read_command
0800cac0 T lsm303d_write_command
0800cad6 T lsm303d_write_reg
0800cb04 T accel_factory_provision
0800cb20 T accel_pin_setup
0800cb44 T lsm303d_setup
0800cd9c T accel_init
0800ce70 T lsm303d_isr1
0800cea8 T accel_power_off
0800cef8 T mag_power_off
0800cf30 T accel_power_on
0800d038 T mag_power_on
0800d114 T lsm303d_acc_update_fs_range
0800d20c T lsm303d_mag_update_fs_range
0800d298 T lsm303d_acc_update_odr
0800d310 T lsm303d_mag_update_odr
0800d39c T accel_read_data
0800d42c T mag_read_data
0800d4a0 T lsm303d_temp_read
0800d568 T accel_thread
0800da9c t accel_fetch_values
0800dae8 t accel_compute_heading
0800ded8 t accel_is_stationary
0800e0d0 t accel_gps_moved
0800e1e8 T accel_should_stay_in_nap_mode
0800e2b0 t heading_change
0800e2f0 T log_accel_deviations
0800e49c T parking_setting_dump
0800e508 T parking_setting
0800e6d8 T parking_settings_init
0800e718 T accel_upsidedown
0800e73c t cache_position
0800e888 t get_oldest_location
0800e8cc T ft_update
0800e90c T _start
0800e934 t __NVIC_SystemReset
0800e960 T board_fault_save
0800e980 T board_fault_check
0800ea34 T os_error
0800ea90 T die
0800ea9c T mbed_assert_internal
0800eac0 T pop_registers_from_fault_stack
0800eb8c T HardFault_Handler
0800eba0 t is_equal
0800eba4 t is_done
0800ebac t .handler2_address_const
0800ebb4 T set_uint16
0800ebe2 T set_uint32
0800ec24 T get_uint64
0800ed8e T store_uint16
0800edc4 T store_uint32
0800ee14 T printf_init
0800ee30 T printf_put
0800ee84 t ee_skip_atoi
0800eed4 t ee_number
0800f0f8 T vsprintf_common
0800f594 T vsprintf
0800f5ce T printf_putchar
0800f604 T printf
0800f658 T sprintf
0800f69c T uart_putchar
0800f6f0 T uart_printf
0800f72c T error
0800f778 T __errno
0800f788 T memset
0800f7c0 T strlen
0800f7ee T strnlen
0800f828 T strcmp
0800f876 T strncmp
0800f8d4 T abs
0800f8f0 T atoi
0800f9e0 T random_setup_seed
0800fa30 T atof
0800fb34 T chip_uid
0800fbd4 T alarm_start_common
0800fc50 T alarm_cancel
0800fcac T alarm_start_periodic
0800fcdc T alarm_start
0800fd0c T rtc_init
0800fe60 T rtc_read
0800ff60 T rtc_write
08010044 T mcu_sleep
08010060 T os_idle_demon
08010068 T datetime_to_seconds
080100b4 t check_if_leap
08010120 T date_to_seconds
080101f8 T seconds_to_datetime
0801037c T datetime_display
080103e0 T timezone_offset
08010448 T event_init
08010464 T event_display
0801061c T event_type_normalize
08010640 T event_store
0801095c T event_log
08010a30 T event_log_reset
08010a48 T event_settings_init
08010a68 T event_setting
08010b2c T event_setting_dump
08010b74 T sys_file_backup_init
08010bc8 T sys_file_backup_write_reg
08010c08 T sys_file_backup_read_reg
08010c48 t sys_file_load_from_backup
08010c88 t sys_file_save_to_backup
08010cc4 T sys_file_sync_to_backup
08010cd0 T sys_file_init
08010da4 T uid_init
08010db8 T uid_copy
08010dd4 T uid_set_me
08010df0 T crc16_with_seed
08010e44 T crc16
08010e64 T announce
08010ed0 T analogin_read_u16
08010f08 t LL_ADC_SetCommonPathInternalCh
08010f30 t _analogin_init_direct
080110a0 T analogin_init
08011128 T adc_read
080112d0 t gpio_write
08011302 t _gpio_init_in
08011342 t _gpio_init_out
0801138c T gpio_init_in
080113ae T gpio_init_in_ex
080113d4 T gpio_init_out
080113f6 T gpio_init_out_ex
0801141a t LL_GPIO_SetPinMode
08011494 T Set_GPIO_Clock
080115b0 T gpio_set
080115f8 T gpio_init
08011680 T gpio_mode
080116a4 T gpio_dir
080116e0 t __NVIC_EnableIRQ
0801171c t __NVIC_DisableIRQ
08011764 t __NVIC_ClearPendingIRQ
080117a0 t __NVIC_SetVector
080117d4 t LL_EXTI_EnableIT_0_31
080117f8 t LL_EXTI_DisableIT_0_31
08011820 t LL_EXTI_EnableRisingTrig_0_31
08011844 t LL_EXTI_DisableRisingTrig_0_31
0801186c t LL_EXTI_IsEnabledRisingTrig_0_31
08011898 t LL_EXTI_EnableFallingTrig_0_31
080118bc t LL_EXTI_DisableFallingTrig_0_31
080118e4 t LL_EXTI_IsEnabledFallingTrig_0_31
08011910 t handle_interrupt_in
08011a20 t gpio_irq0
08011a30 t gpio_irq1
08011a40 t gpio_irq2
08011a50 t gpio_irq3
08011a60 t gpio_irq4
08011a70 t gpio_irq5
08011a80 t gpio_irq6
08011a90 T gpio_irq_init
08011c90 T gpio_irq_set
08011d54 T gpio_irq_enable
08011e30 T gpio_irq_disable
08011f1c t LL_GPIO_SetPinMode
08011f96 t LL_GPIO_GetPinMode
08012004 t LL_GPIO_SetPinOutputType
08012034 t LL_GPIO_SetPinSpeed
080120ae t LL_GPIO_SetPinPull
08012128 t LL_GPIO_SetAFPin_0_7
080121a2 t LL_GPIO_SetAFPin_8_15
08012220 t stm_pin_DisconnectDebug
08012236 t stm_pin_PullConfig
0801227c t stm_pin_SetAFPin
080122c4 T pin_function
080123f4 T pin_mode
080124b8 T pinmap_merge
080124f8 T pinmap_find_peripheral
0801253c T pinmap_peripheral
08012584 T pinmap_find_function
080125c8 t debug
080125dc t _serial_init_direct
08012760 T serial_init
08012800 T serial_baud
08012864 T serial_format
08012938 T init_uart
08012a00 T get_uart_index
08012a7c T clock_read
08012a8c T rtc_save
08012a9a T rtc_reset_time
08012aa8 T kinetis_flash_erase_sector
08012abe T kinetis_flash_write
08012ad8 T core_util_critical_section_exit
08012ae6 T core_util_critical_section_enter
08012af4 T bus_frequency
08012b08 T stm_flash_erase_sector
08012b1e T stm_flash_write
08012b38 T stm_flash_read
08012b52 T stm_flash_keep_on
08012b68 T serial_read_timeout
08012b84 T serial_read_timeout_signal
08012ba0 T serial_write
08012bb8 T serial_write_direct
08012bd0 T serial_hold
08012be4 T serial_release
08012bf8 T serial_flush
08012c0c T serial_buffer
08012c26 T serial_line_mode
08012c3c T serial_enable_dma_tx
08012c50 T serial_power_off
08012c64 T serial_power_on
08012c78 t ITM_SendChar
08012cc8 T SWO_Init
08012d08 T ITM_SendString
08012d32 T ITM_SendStringSized
08012d68 W HAL_InitTick
08012de0 W HAL_GetTick
08012df8 t __NVIC_GetPriorityGrouping
08012e14 t __NVIC_SetPriority
08012e68 t NVIC_EncodePriority
08012ed0 t SysTick_Config
08012f14 T HAL_NVIC_SetPriority
08012f4c T HAL_SYSTICK_Config
08012f64 T HAL_PWR_EnableBkUpAccess
08012f84 T HAL_PWREx_GetVoltageRange
08012fa0 T HAL_RTC_Init
08013096 W HAL_RTC_MspInit
080130aa T HAL_RTC_SetTime
080131e4 T HAL_RTC_SetDate
080132f2 T HAL_RTC_WaitForSynchro
0801333e T RTC_EnterInitMode
080133a4 T RTC_ExitInitMode
08013420 T RTC_ByteToBcd2
08013460 T RTC_Bcd2ToByte
08013494 T HAL_RTCEx_EnableBypassShadow
08013500 T HAL_RTCEx_BKUPWrite
08013532 T HAL_RTCEx_BKUPRead
08013560 T HAL_RCC_OscConfig
08013da0 T HAL_RCC_GetSysClockFreq
08013eb8 T HAL_RCC_GetHCLKFreq
08013ed0 T HAL_RCC_GetPCLK1Freq
08013efc T HAL_RCC_GetPCLK2Freq
08013f28 t RCC_SetFlashLatencyFromMSIRange
08013fe8 T HAL_RCCEx_PeriphCLKConfig
08014418 t RCCEx_PLLSAI1_Config
080145fc T HAL_UART_Init
08014698 W HAL_UART_MspInit
080146ac T UART_SetConfig
08014b64 T UART_AdvFeatureConfig
08014ca8 T UART_CheckIdleState
08014d3a T UART_WaitOnFlagUntilTimeout
08014e32 T HAL_UARTEx_DisableClockStopMode
08014e76 T HAL_UARTEx_EnableStopMode
08014eba T HAL_UARTEx_DisableStopMode
08014efe t LL_ADC_SetCommonClock
08014f24 t LL_ADC_SetCommonPathInternalCh
08014f4a t LL_ADC_GetCommonPathInternalCh
08014f68 t LL_ADC_SetOffset
08014fb0 t LL_ADC_GetOffsetChannel
08014fdc t LL_ADC_SetOffsetState
08015012 t LL_ADC_REG_IsTriggerSourceSWStart
08015038 t LL_ADC_REG_SetSequencerRanks
08015090 t LL_ADC_SetChannelSamplingTime
080150e8 t LL_ADC_SetChannelSingleDiff
08015130 t LL_ADC_DisableDeepPowerDown
08015154 t LL_ADC_IsDeepPowerDownEnabled
0801517c t LL_ADC_EnableInternalRegulator
080151a4 t LL_ADC_IsInternalRegulatorEnabled
080151cc t LL_ADC_Enable
080151f4 t LL_ADC_Disable
0801521c t LL_ADC_IsEnabled
08015242 t LL_ADC_IsDisableOngoing
08015268 t LL_ADC_REG_StartConversion
08015290 t LL_ADC_REG_IsConversionOngoing
080152b6 t LL_ADC_INJ_IsConversionOngoing
080152dc T HAL_ADC_Init
0801556c W HAL_ADC_MspInit
08015580 T HAL_ADC_Start
08015646 T HAL_ADC_PollForConversion
08015764 T HAL_ADC_GetValue
08015780 T HAL_ADC_ConfigChannel
08015f40 T ADC_Enable
08016004 T ADC_Disable
080160c2 t LL_ADC_GetCalibrationFactor
080160f2 t LL_ADC_StartCalibration
08016124 t LL_ADC_IsCalibrationOnGoing
0801614c T HAL_ADCEx_Calibration_Start
0801620c T HAL_ADCEx_Calibration_GetValue
0801622c T __aeabi_drsub
08016234 T __aeabi_dsub
08016234 T __subdf3
08016238 T __adddf3
08016238 T __aeabi_dadd
080164b0 T __aeabi_ui2d
080164b0 T __floatunsidf
080164d0 T __aeabi_i2d
080164d0 T __floatsidf
080164f4 T __aeabi_f2d
080164f4 T __extendsfdf2
08016538 T __aeabi_ul2d
08016538 T __floatundidf
08016548 T __aeabi_l2d
08016548 T __floatdidf
080165a4 T __aeabi_dmul
080165a4 T __muldf3
080167f8 T __aeabi_ddiv
080167f8 T __divdf3
080169c8 T __gedf2
080169c8 T __gtdf2
080169d0 T __ledf2
080169d0 T __ltdf2
080169d8 T __cmpdf2
080169d8 T __eqdf2
080169d8 T __nedf2
08016a54 T __aeabi_cdrcmple
08016a64 T __aeabi_cdcmpeq
08016a64 T __aeabi_cdcmple
08016a74 T __aeabi_dcmpeq
08016a88 T __aeabi_dcmplt
08016a9c T __aeabi_dcmple
08016ab0 T __aeabi_dcmpge
08016ac4 T __aeabi_dcmpgt
08016ad8 T __aeabi_d2iz
08016ad8 T __fixdfsi
08016b28 T __aeabi_d2uiz
08016b28 T __fixunsdfsi
08016b68 T __aeabi_d2f
08016b68 T __truncdfsf2
08016c08 T __aeabi_uldivmod
08016c38 T __popcountsi2
08016c60 T __udivmoddi4
08016f1c W __aeabi_idiv0
08016f1c W __aeabi_ldiv0
08016f20 T sin
08016fb0 T atan2
08016fb4 T modf
08017068 T __ieee754_rem_pio2
08017490 T __kernel_rem_pio2
08017cbc T acos
08017d28 T scalbn
08017e3c T sqrt
08017ea8 T __kernel_sin
08018028 T cos
080180b0 T __ieee754_sqrt
080182a0 T floor
080183a8 T __ieee754_atan2
08018550 T tan
080185b8 T asin
08018620 T truncf
0801865c T __fpclassifyd
080186b8 T atan
08018a08 T __kernel_cos
08018c78 T __ieee754_asin
08019100 T __ieee754_acos
080195d8 T fabs
080195e8 T nan
080195f8 T __kernel_tan
08019a10 T __aeabi_dcmpun
08019a10 T __unorddf2
08019a3c T os_maxtaskrun
08019a40 T os_stackinfo
08019a44 T os_rrobin
08019a48 T os_trv
08019a4c T os_flags
08019a50 T os_clockrate
08019a54 T mp_tcb_size
08019a58 T mp_stk_size
08019a5c T os_stack_sz
08019a60 T os_fifo_size
08019a64 T os_thread_def_osTimerThread
08019a74 T os_messageQ_def_osTimerMessageQ
08019a7c T mp_tmr_size
08019a80 T AHBPrescTable
08019a90 T APBPrescTable
08019a98 T MSIRangeTable
08019ac8 T os_thread_def_reboot_thread
08019ad8 T os_thread_def_gps_thread
08019ae8 T os_thread_def_helm_thread
08019af8 T os_thread_def_modem_thread
08019b08 T os_thread_def_accel_thread
08019fb8 T gps_run_mode_settings
08019fd0 T gps_send_flags
0801a538 T os_timer_def_reboot_timer
0801a5d8 T os_mutex_def_battery_mutex
0801b184 T provision_devices
0801b398 T setting_commands
0801b854 T os_mutex_def_printf_mutex
0801b90c T days_per_month
0801b93c T days_in_month
0801bb2c T os_mutex_def_event_display_mutex
0801bba0 T crc16_table
0801bfc4 T pin_lines_desc
0801c084 V PinMap_ADC
0801c108 V PinMap_ADC_Internal
0801c12c V PinMap_UART_TX
0801c180 V PinMap_UART_RX
0801c1e0 T ll_pin_defines
0801c36c T build_timestamp
0801c370 T build_tag
0801c374 T build_target_version
0801c378 t npio2_hw
0801c3f8 t two_over_pi
0801c500 t init_jk
0801c510 t PIo2
0801c558 t one
0801c560 t tiny
0801c568 t CSWTCH.8
0801c580 t CSWTCH.9
0801c598 t atanhi
0801c5b8 t atanlo
0801c5d8 T firmware_move_into_place
0801c654 T memcpy
0801c722 T __exidx_start
0801c72c R __etext
0801c72c R __exidx_end
0801c72c R _sidata
20000190 D __data_start__
20000190 D os_thread_def_main
20000190 D _sdata
200001a0 D SystemCoreClock
200001a4 D gps_capture_location
200001a8 D gps_run_mode_labels
200001b4 D gps_settings_commands
200001ec D helm_jobs
2000023c d state_names
2000026c D upload_partitions
2000029c D board_log_params
200002cc D partitions
200002dc D boatlog_partition
2000032c D event_partition
2000037c D firmware_sensor_partition
200003cc D firmware_partition
2000041c D gsm_enabled
20000420 D nap_mode_labels
20000430 D lsm303d_acc_odr_table
20000478 D lsm303d_mag_odr_table
200004a8 d status_registers
200004e5 D enable_park
200004e6 d accel_gps_override
200004e8 d call_count.0
200004ec d lower_digits
200004f0 d upper_digits
200004f4 D random_seed
200004f8 d level_names
20000508 d source_names
20000568 D event_show
2000056c D sys_reg_file_ptr
20000570 D stm_flash_device
20000588 D uwTickPrio
2000058c D uwTickFreq
20000590 D __data_end__
20000590 D _edata
200005a0 B __bss_start__
200005a0 B mp_tcb
200005a0 B _sbss
200005a0 B __uninitialized_end
200005a0 B __uninitialized_start
20000770 B mp_stk
20002620 B os_stack_mem
20002bb8 B os_fifo
20002c3c B os_active_TCB
20002c5c B osThreadId_osTimerThread
20002c60 B os_messageQ_q_osTimerMessageQ
20002c90 B osMessageQId_osTimerMessageQ
20002c94 B m_tmr
20002c98 B os_initialized
20002c99 B os_running
20002c9c B os_timer_head
20002ca0 B os_rdy
20002cb8 B os_dly
20002cd0 B os_robin
20002cd8 B os_tick_irqn
20002cdc b os_lock
20002cdd b os_psh_flag
20002ce0 B os_tsk
20002ce8 B os_idle_TCB
20002d20 B os_time
20002d24 B reboot_tid
20002d28 B gps_tid
20002d2c B helm_tid
20002d30 B modem_tid
20002d34 B accel_tid
20002d38 B console_tid
20002d3c B gps_stats
20002d48 B helm_stats
20002d4c B log_stats
20002d68 B demo_mode
20002d6c B the_buffer
2000316c B gps_running
2000316d B gps_show_bytes
2000316e B gps_watch_location
2000316f B gps_settings_update
20003170 B gps_state
20003171 B gps_state_requested
20003172 B gps_tracking
20003174 B gps_idle_timestamp
20003178 B gps_have_fix
20003179 B gps_acquired_fix
2000317a B gps_high_res
2000317c B gps_fix_time
20003180 B time_acquired
20003188 B gps_loc
20003338 B gps_lat
2000333c B gps_lon
20003340 B previous_lat
20003348 B previous_lng
20003350 B gps_current_lat
20003358 B gps_current_lon
20003360 B gps_current_pdop
20003368 B time_checked
2000336c B gps_attr
20003384 B gps_op
200033d4 B gps_data
20003424 B helm_watch
20003425 B helm_actions
20003426 B helm_running
20003428 B helm_alarm
2000343c b ra.1
2000343d b ls.0
20003440 B gsm_status
2000344a B modem_thread_running
2000344b B modem_thread_hold
2000344c B modem_sleep
20003450 B modem_last_contact
20003454 B modem_last_power_up
20003458 B modem_alarm
20003470 B modem_info
20003480 B modem_state
20003481 B reboot_pending
20003484 B reboot_count
20003488 B os_timer_cb_reboot_timer
200034a0 B reboot_timer_id
200034a4 B bat_sw_pin
200034c0 B power_charge_pin
200034dc B power_fault_pin
200034f8 B bat_an_pin
20003560 B battery_mutex
20003564 B os_mutex_cb_battery_mutex
20003574 B battery_initted
20003575 B board_info_have_imei
20003578 B board_info_imei
20003580 B lat_current
20003584 B lon_current
20003588 B time_current
2000358c B last_sensor_time
20003590 B boat_log_buffer
200035d0 b count.0
200035d4 B hs_encoder
200039e4 B compress_buffer
200042ac B firmware_updated
200042b0 B firmware_sensor_header
200042bc B firmware_sensor_read
200042d0 B firmware_sensor_cache
20004ad0 B the_cache
200052d0 b boatlog_buffer
20005ad0 b event_buffer
200062d0 B modem_initted
200062d1 B modem_pin_initted
200062d4 B modem_power_key
200062f0 B modem_power_en_pin
2000630c B modem_port
20006394 B modem_watch
20006398 B modem_tx_buffer
20006418 B modem_rx_buffer
20006498 b errmsg.0
2000649e B modem_log_in
2000649f B modem_log_out
200064a0 B modem_log_count
200064a4 B modem_log
200064a8 B modem_logs
20006588 B nap_sleep_time
2000658c B board_power_state
20006590 B provision_sleep
20006594 B set_parse_ptr
20006598 B set_parse_size
2000659c B set_parse_line
2000661c B settings
200067a8 B solar_an_pin
20006810 B sunrise
20006814 B sunset
20006818 B power_bitmask
2000681c B gps_en_pin
20006838 B gps_vbckp_pin
20006854 B gps_reset_pin
20006870 B gps_pps_pin
2000688c B gps_initted
2000688d B gps_pin_initted
20006890 B gps_port
20006918 B gps_tx_buffer
20006938 B gps_rx_buffer
200069b8 B lsm303d_int
20006a18 B lsm303d_sensitivity_acc
20006a1a B lsm303d_sensitivity_mag
20006a1c B accel_initted
20006a1d B accel_pin_initted
20006a20 B lsm303d_int1_pin
20006a3c B lsm303d_int1_irq
20006a4c B accel_is_on
20006a4d B mag_is_on
20006a4e B device_raw_heading
20006a50 B last_heading
20006a52 B heading_current
20006a54 B device_pitch
20006a56 B device_roll
20006a58 B last_accel
20006a60 B last_mag
20006a68 B accel_current
20006a70 B mag_current
20006a76 B device_capsized
20006a77 b waiting_to_park
20006a78 b gps_ignored
20006a80 b locations
20006b20 b loc_idx
20006b21 b got_five
20006b24 b deviations
20006b34 B flipped_bits
20006b38 B moving_bits
20006b3c b stationary_time.1
20006b3e B board_initted
20006b40 B fault_r0
20006b44 B fault_r1
20006b48 B fault_r2
20006b4c B fault_r3
20006b50 B fault_r12
20006b54 B fault_lr
20006b58 B fault_pc
20006b5c B fault_psr
20006b60 B board_fault
20006b64 B printf_mutex
20006b68 B os_mutex_cb_printf_mutex
20006b78 B alarm_head
20006b7c b RTC_inited
20006b80 b RtcHandle
20006ba4 B event_display_mutex
20006ba8 B os_mutex_cb_event_display_mutex
20006bb8 B event_buffer
20006c38 B event_time
20006c3c B event_in
20006c3d B event_out
20006c40 B event_entries
20008240 b sys_reg_file_data
20008258 b hrtc_backup
2000827c b backup_domain_initialized
20008280 B board_uid
2000828c b irq_handler
20008290 b irq_channel_used
20008294 b channels
200084a8 B stdio_uart_inited
200084ac B stdio_uart
20008534 B uart_handlers
200086c0 B epoch_clock
200086c4 B console_uart
2000874c B uwTick
20008750 B __bss_end__
20008750 B _ebss
20008750 ? __end__
2000fc00 ? __HeapLimit
2000fc00 A __StackLimit
20010000 A _estack
20010000 B __StackTop
f1e0f85f a BootRAM
