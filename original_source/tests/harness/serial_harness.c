#include <stdio.h>
#include <stdint.h>
#include <setjmp.h>
#include <stdlib.h>
#include <string.h>
#include "pelagic-types.h"
#include "serial_harness.h"
#include "serial_api.h"
#include "PinNames.h"
#include "hardware.h"
#include "stats.h"

typedef struct {
    serial_stream_t *receive;
    serial_stream_t *transmit;
    jmp_buf *jump;
    char    *capture_buffer;
    int     captured_bytes;
} serial_device_t;

static serial_device_t serial_devices[3];

int helm_tid = 0;
struct gps_stats gps_stats;

void serial_stream(serial_port_t port, serial_stream_t *receive, serial_stream_t *transmit, jmp_buf *jump) {
    serial_device_t *dev = &serial_devices[port];

    dev->receive = receive;
    dev->transmit = transmit;
    dev->jump = jump;
}

void serial_teardown(serial_port_t port) {
    serial_device_t *dev = &serial_devices[port];

    dev->receive = NULL;
    dev->transmit = NULL;
    dev->jump = NULL;
    dev->capture_buffer = NULL;
}
void serial_capture(serial_port_t port, void *buffer) {
    serial_device_t *dev = &serial_devices[port];

    dev->capture_buffer = (char *)buffer;
    dev->captured_bytes = 0;
}

int serial_captured_bytes(serial_port_t port) {
    return serial_devices[port].captured_bytes;
}

void serial_init(serial_t *obj, PinName tx, PinName rx) {
    switch (tx) {
    case GPS_TX:
        obj->index = GPS_PORT;
        break;

    case MDM_TX:
        obj->index = GSM_PORT;
        break;

    case CONSOLE_TX:
        obj->index = CONSOLE_PORT;
        break;

    default:
        printf("serial_init: unknown pin %d\n", tx);
        exit(-1);
    }
}

int  serial_read(serial_t *obj, char *buffer, int bytes) {
    serial_device_t *dev = &serial_devices[obj->index];
    serial_sequence_t *seq;
    serial_stream_t *stream;

    if ((stream = dev->receive) == NULL) {
        printf("serial_read: no stream setup for port %d\n", obj->index);
        exit(-1);
    }

    if (stream->index == stream->count) {
        longjmp(*dev->jump, 1);   // done - jump back
    }

    seq = &stream->sequences[stream->index];

    if (seq->bytes == 0) {  // Timeout simulation.
        stream->index++;
        return 0;
    }

    if (bytes < seq->bytes) {
//        printf("sequence %d: requested byte count [%d] is less than what's available [%d]]\n", stream->index, bytes, seq->bytes);
    }

    if (bytes > seq->bytes)
        bytes = seq->bytes;

    memcpy(buffer, seq->data, bytes);

    stream->index++;

    return bytes;
}

int  serial_read_timeout(serial_t *obj, char *buffer, int bytes, uint32_t milliseconds) {
    return serial_read(obj, buffer, bytes);
}

void serial_write(serial_t *obj, char *buffer, int bytes) {
    serial_device_t *dev = &serial_devices[obj->index];

    if (dev->capture_buffer == NULL) {
        return;
    }

    memcpy(dev->capture_buffer + dev->captured_bytes, buffer, bytes);
    dev->captured_bytes += bytes;
}


void serial_baud(serial_t *obj, int baudrate) {
    return;
}


void serial_format(serial_t *obj, int data_bits, SerialParity parity, int stop_bits) {
    return;
}


void serial_buffer(serial_t *obj, char *tx_buffer, int tx_size, char *rx_buffer, int rx_size) {
}

void serial_enable_dma_rx(serial_t *obj, char *dma_buffer, int dma_size) {
}

void serial_enable_dma_tx(serial_t *obj) {
}

void serial_suspend_dma_rx(serial_t *obj) {
}

void serial_hold(serial_t *obj) {

}

void serial_release(serial_t *obj) {

}

void serial_resume_dma_rx(serial_t *obj) {
}

void serial_flush(serial_t *obj) {

}

void serial_power_on(serial_t *obj) {

}


void serial_power_off(serial_t *obj) {

}
