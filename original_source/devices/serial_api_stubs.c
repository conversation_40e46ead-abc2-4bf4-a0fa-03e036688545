#include "serial_api_stubs.h"

#include "mbed_assert.h"
#include "pelagic-types.h"
#include "serial_api.h"

#include "cmsis.h"
#include "pinmap.h"
#include "clk_freqs.h"
#include "PeripheralPins.h"

#include "dma.h"

#include "stats.h"
#include "signals.h"
#include "hardware.h"

#include "semihost_api.h"

int8_t serial_semihost = 0;
serial_t console_uart;

// void serial_init       (serial_t *obj, PinName tx, PinName rx) {}
// void serial_free       (serial_t *obj) {}
// void serial_baud       (serial_t *obj, int baudrate) {}
// void serial_format     (serial_t *obj, int data_bits, SerialParity parity, int stop_bits) {}

// int  serial_getc       (serial_t *obj) {return 0;}
// void serial_putc       (serial_t *obj, int c) {}
// int  serial_readable   (serial_t *obj) {return 0;}
// int  serial_writable   (serial_t *obj) {return 0;}

int  serial_available  (serial_t *obj) 
{
    return 0;
}

int  serial_read       (serial_t *obj, char *buffer, int bytes) 
{
    return 0;
}

int  serial_read_timeout(serial_t *obj, char *buffer, int bytes, uint32_t milliseconds) 
{
    return 0;
}

int serial_read_signal(serial_t *obj, char *buffer, int bytes, uint16_t *signals) 
{
    return 0;
}

int  serial_read_timeout_signal(serial_t *obj, char *buffer, int bytes, uint32_t milliseconds, uint16_t *signals) 
{
    return 0;
}

void serial_write      (serial_t *obj, char *buffer, int bytes) 
{

}

void serial_write_direct(serial_t *obj, char *buffer, int bytes) 
{

}

void serial_hold       (serial_t *obj) 
{

}

void serial_release    (serial_t *obj) 
{

}

// TODO Ian:
void serial_flush      (serial_t *obj) 
{

}

void serial_buffer     (serial_t *obj, char *tx_buffer, int tx_size, char *rx_buffer, int rx_size) 
{

}

void serial_line_mode  (serial_t *obj, int line) 
{
    // See RM0394 pg. 1238, Modbus/ASCII for some details on CR/LF handling
    // USART->CR2 ADD[7:0] is a match character (like \n) 
    // CMF flag must be set for this to trigger an interrupt on character reception

}

void serial_enable_dma_rx(serial_t *obj, char *dma_buffer, int dma_size) 
{
    // See RM0394 pg. 1252 for DMA RX
}

void serial_enable_dma_tx(serial_t *obj) 
{
    // See RM0394 pg. 1251 for DMA TX
}

void serial_suspend_dma_rx(serial_t *obj) 
{

}

void serial_resume_dma_rx(serial_t *obj) 
{

}


void serial_power_off(serial_t *obj) 
{
    serial_free(obj); // does the same thing as RCC section below. 

    #ifdef USART_PERIPHERAL_POWERDOWN
    //! Requirements for disabling USART based on RM0394 pg. 1263
    // 1. TE bit must be reset                  USART_CR1_TE
    // 2. USART_ISR TC bit must be set          USART_ISR_TC
    // 3. UE bit must be reset                  USART_CR1_UE
    // --> USART enters low power mode
    //! See RM0394 pg. 1257 for USART behavior in Low Power modes
    // Register content is kept in these low power modes
    //  - Sleep
    //  - LP Run
    //  - Stop 0/1
    //  - Stop 2    -- USART must be disabled or put in reset state
    // Regster contents MUST be reinitialized after
    //  - Standby
    //  - Shutdown
    USART_TypeDef *huart =  ((uint32_t*)(obj->serial.uart) == UART_1) ? USART1 : 
                            ((uint32_t*)(obj->serial.uart) == UART_2) ? USART2 : 
                            ((uint32_t*)(obj->serial.uart) == UART_3) ? USART3 : 
                            ((uint32_t*)(obj->serial.uart) == LPUART_1) ? LPUART1 : NULL;   
    MBED_ASSERT(huart != NULL);
    // Prepare by disabling TX nicely
    LL_USART_DisableDirectionTx(huart);                     // USART_CR1_TE
    while (!READ_BIT(huart->ISR, USART_ISR_TC)) {}
    // Actually disable
    LL_USART_Disable(huart);                                // USART_CR1_UE
    //! Unknowns
    // CR1->UESM    LL_USART_DisableStopMode
    // CR3->UCESM   LL_USART_DisableClockInStopMode
    // CR3->WUFIE   LL_USART_DisableIT_WakeUp               // only when clock is HSI16 or LSE
    #endif

    //! This is what serial_free does
    #ifdef USART_RCC_POWERDOWN
    #if defined(USART1_BASE)
    if (obj_s->uart == UART_1) {
        __HAL_RCC_USART1_FORCE_RESET();
        __HAL_RCC_USART1_RELEASE_RESET();
        __HAL_RCC_USART1_CLK_DISABLE();
    }
    #endif
    #if defined(USART2_BASE)
    if (obj_s->uart == UART_2) {
        __HAL_RCC_USART2_FORCE_RESET();
        __HAL_RCC_USART2_RELEASE_RESET();
        __HAL_RCC_USART2_CLK_DISABLE();
    }
    #endif
    #if defined(USART3_BASE)
    if (obj_s->uart == UART_3) {
        __HAL_RCC_USART3_FORCE_RESET();
        __HAL_RCC_USART3_RELEASE_RESET();
        __HAL_RCC_USART3_CLK_DISABLE();
    }
    #endif
    #if defined(LPUART1_BASE)
    if (obj_s->uart == LPUART_1) {
        __HAL_RCC_LPUART1_FORCE_RESET();
        __HAL_RCC_LPUART1_RELEASE_RESET();
        __HAL_RCC_LPUART1_CLK_DISABLE();
    }
    #endif
    #endif

    // Configure GPIOs back to reset value
    pin_function(obj_s->pin_tx, STM_PIN_DATA(STM_MODE_ANALOG, GPIO_NOPULL, 0));
    pin_function(obj_s->pin_rx, STM_PIN_DATA(STM_MODE_ANALOG, GPIO_NOPULL, 0));

}

// REQUIRES serial_init has been called at least once!
//! OR just don't call this function at all!
void serial_power_on(serial_t *obj) 
{
    struct serial_s *obj_s = SERIAL_S(obj);
    serial_init(obj, obj_s->pin_tx, obj_s->pin_rx);
    // calls _serial_init_direct(serial_t *obj, const serial_pinmap_t *pinmap)
            // struct serial_s *obj_s = SERIAL_S(obj);
                // Get the peripheral name (UART_1, UART_2, ...) from the pin and assign it to the object
            // obj_s->uart = (UARTName)pinmap->peripheral;
            // MBED_ASSERT(obj_s->uart != (UARTName)NC);
                // Reset and enable clock
            // #if defined(USART1_BASE)
            //     if (obj_s->uart == UART_1) {
            //         __HAL_RCC_USART1_CLK_ENABLE();
            //     }
            // #endif
                // ... repeat for all UARTs
                // Assign serial object index
            // obj_s->index = get_uart_index(obj_s->uart);
            // MBED_ASSERT(obj_s->index >= 0); // TODO Ian: this returns 255 which is presumably an error code
                // Configure UART pins
            // pin_function(pinmap->tx_pin, pinmap->tx_function);
            // pin_mode(pinmap->tx_pin, PullUp);
            // pin_function(pinmap->rx_pin, pinmap->rx_function);
            // pin_mode(pinmap->rx_pin, PullUp);
                // Configure UART
            // obj_s->baudrate = 9600;
            // obj_s->databits = UART_WORDLENGTH_8B;
            // obj_s->stopbits = UART_STOPBITS_1;
            // obj_s->parity   = UART_PARITY_NONE;
            // obj_s->pin_tx = pinmap->tx_pin;
            // obj_s->pin_rx = pinmap->rx_pin;
    // calls init_uart(serial_t *obj)
            // struct serial_s *obj_s = SERIAL_S(obj);
            // UART_HandleTypeDef *huart = &uart_handlers[obj_s->index];
            // huart->Instance = (USART_TypeDef *)(obj_s->uart);
            // #if defined(LPUART1_BASE)
            //     if (huart->Instance == LPUART1) {
            //         if (obj_s->baudrate <= 9600) {
            // #if ((MBED_CONF_TARGET_LPUART_CLOCK_SOURCE) & USE_LPUART_CLK_LSE) && defined(USART_CR3_UCESM)
            //             HAL_UARTEx_EnableClockStopMode(huart);
            // #endif
            //             HAL_UARTEx_EnableStopMode(huart);
            //         } else {
            // #if defined(USART_CR3_UCESM)
            //             HAL_UARTEx_DisableClockStopMode(huart);
            // #endif
            //             HAL_UARTEx_DisableStopMode(huart);
            //         }
            //     }
            // #endif
    // calls HAL_UART_Init(UART_HandleTypeDef *huart)
            // /* Check the UART handle allocation */
            // if (huart == NULL) return HAL_ERROR;
            // assert_param((IS_UART_INSTANCE(huart->Instance)) || (IS_LPUART_INSTANCE(huart->Instance)));
            // /* Allocate lock resource and initialize it */
            // if (huart->gState == HAL_UART_STATE_RESET) huart->Lock = HAL_UNLOCKED;
            //     //! ALLEGEDLY Init the low level hardware : GPIO, CLOCK ...
            //     HAL_UART_MspInit(huart); //! actually does nothing!
            // huart->gState = HAL_UART_STATE_BUSY;
            // __HAL_UART_DISABLE(huart);
            // /* Set the UART Communication parameters */
            // UART_SetConfig(huart)
               // In asynchronous mode, the following bits must be kept cleared:
            // CLEAR_BIT(huart->Instance->CR2, (USART_CR2_LINEN | USART_CR2_CLKEN));
            // CLEAR_BIT(huart->Instance->CR3, (USART_CR3_SCEN | USART_CR3_HDSEL | USART_CR3_IREN));
            // __HAL_UART_ENABLE(huart);
            // /* TEACK and/or REACK to check before moving huart->gState and huart->RxState to Ready */
            // return (UART_CheckIdleState(huart));


    //! If the clock was stopped from RCC, are the registers cleared?
    // Probably not. Register contents are retained when in STOP 0/1/2, where all clocks off except LSI/LSE
    // Would have to test to be sure.
    // If not, then power down should be clock gating from RCC
    #ifdef RCC_PERIPHERAL_POWERDOWN
        if (obj_s->uart == UART_1) {
            __HAL_RCC_USART1_CLK_ENABLE();
        }
        if (obj_s->uart == UART_2) {
            __HAL_RCC_USART2_CLK_ENABLE();
        }

        if (obj_s->uart == UART_3) {
            __HAL_RCC_USART3_CLK_ENABLE();
        }
        if (obj_s->uart == LPUART_1) {
            __HAL_RCC_LPUART1_CLK_ENABLE();
        }
    #endif

}


void serial_console_init(bool power_on) 
{
    // pass
}
