/* mbed Microcontroller Library
 * Copyright (c) 2006-2013 ARM Limited
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef MBED_SERIAL_API_STUBS_H
#define MBED_SERIAL_API_STUBS_H

#include "device.h"
#include "serial_api.h"
#include "cmsis_os.h"
#include "stm32l4xx.h"

// #if DEVICE_SERIAL

#ifdef __cplusplus
extern "C" {
#endif

typedef struct serial_s serial_t;

void serial_init       (serial_t *obj, PinName tx, PinName rx);
void serial_free       (serial_t *obj);
void serial_baud       (serial_t *obj, int baudrate);
void serial_format     (serial_t *obj, int data_bits, SerialParity parity, int stop_bits);

int  serial_getc       (serial_t *obj);
void serial_putc       (serial_t *obj, int c);
int  serial_readable   (serial_t *obj);
int  serial_writable   (serial_t *obj);

int  serial_available  (serial_t *obj);
int  serial_read       (serial_t *obj, char *buffer, int bytes);
int  serial_read_timeout(serial_t *obj, char *buffer, int bytes, uint32_t milliseconds);
int serial_read_signal(serial_t *obj, char *buffer, int bytes, uint16_t *signals);
int  serial_read_timeout_signal(serial_t *obj, char *buffer, int bytes, uint32_t milliseconds, uint16_t *signals);
void serial_write      (serial_t *obj, char *buffer, int bytes);
void serial_write_direct(serial_t *obj, char *buffer, int bytes);
void serial_hold       (serial_t *obj);
void serial_release    (serial_t *obj);
void serial_flush      (serial_t *obj);
void serial_buffer     (serial_t *obj, char *tx_buffer, int tx_size, char *rx_buffer, int rx_size);
void serial_line_mode  (serial_t *obj, int line);
void serial_enable_dma_rx(serial_t *obj, char *dma_buffer, int dma_size);
void serial_enable_dma_tx(serial_t *obj);
void serial_suspend_dma_rx(serial_t *obj);
void serial_resume_dma_rx(serial_t *obj);

void serial_power_off(serial_t *obj);
void serial_power_on(serial_t *obj);

void serial_console_init(bool power_on);

extern int8_t serial_semihost;

#define SERIAL_DMA_CHUNKS       2

struct serial_s {
        uint8_t      index;
        uint8_t      line_mode;      // if operating in line mode (only notify when \n is received)
        volatile bool  holding;       // set if tx is being held until full or released
        volatile bool  transmitting;  // set if tx is active
        bool         is_on;

        uint16_t      pin_tx, pin_rx;
        uint32_t        stopbits, parity, baudrate, databits;

        osMutexId   lock;

        UARTName    uart;             // TODO Ian: Seems like the right Uart type based on errors in serial_api.c

        IRQn_Type   irq_n;          // which IRQ this is using

        char            *rx_buffer;     // ring buffer for receive
        int              rx_size;        // ring buffer size
        uint32_t         rx_size_mask;
        volatile int     rx_in, rx_out, rx_count;   // in & out indexes plus current byte count

        char            *tx_buffer;    // ring buffer for tx
        int             tx_size;       // ring buffer tx size
        uint32_t        tx_size_mask;
        volatile int    tx_in, tx_out; // in & out pointers

        volatile osThreadId  rx_waiting, tx_waiting;    // threads waiting for completions

        IRQn_Type   dma_irq_n;      // Which DMA IRQ

        uint8_t         dma_enabled;

        struct DMA_Channel   *dma_rx_regs;
        __IO uint8_t   *dmamux_rx_reg;
        uint8_t         dmamux_rx_slot;

        char            *dma_rx_buffers[SERIAL_DMA_CHUNKS];
        uint8_t         dma_rx_size;
        uint8_t         dma_rx_index;

        struct DMA_Channel   *dma_tx_regs;
        __IO uint8_t   *dmamux_tx_reg;
        uint8_t         dmamux_tx_slot;
        int             dma_tx_xfer;
};

// above = boatos implementation
// below = mbed implementation
// typedef struct {
//     struct serial_s serial;  /**< Target specific serial structure */
//     struct buffer_s tx_buff; /**< TX buffer */
//     struct buffer_s rx_buff; /**< RX buffer */
//     uint8_t char_match;      /**< Character to be matched */
//     uint8_t char_found;      /**< State of the matched character */
// } serial_t;

#ifdef __cplusplus
}
#endif

// #endif

#endif // MBED_SERIAL_API_STUBS_H
